import { unescapeLatexFormulas } from '@/utils/latexUtils';

/**
 * 测试LaTeX公式反斜杠转义修复
 */
describe('unescapeLatexFormulas', () => {
  test('应该正确处理LaTeX公式中被错误转义的反斜杠', () => {
    // 测试用例1: 行间公式中的反斜杠转义
    const input1 = '$$f\\\\left(x\\_{2}\\\\right)-f\\\\left(x\\_{1}\\\\right)=f^{\\\\prime}(\\\\xi)\\\\left(x\\_{2}-x\\_{1}\\\\right)$$';
    const expected1 = '$$f\\left(x_{2}\\right)-f\\left(x_{1}\\right)=f^{\\prime}(\\xi)\\left(x_{2}-x_{1}\\right)$$';
    const result1 = unescapeLatexFormulas(input1);
    
    console.log('测试用例1:');
    console.log('输入:', input1);
    console.log('期望:', expected1);
    console.log('实际:', result1);
    console.log('匹配:', result1 === expected1);
    
    expect(result1).toBe(expected1);
  });

  test('应该正确处理行内公式中被错误转义的反斜杠', () => {
    // 测试用例2: 行内公式中的反斜杠转义
    const input2 = '$\\\\alpha + \\\\beta = \\\\gamma$';
    const expected2 = '$\\alpha + \\beta = \\gamma$';
    const result2 = unescapeLatexFormulas(input2);
    
    console.log('测试用例2:');
    console.log('输入:', input2);
    console.log('期望:', expected2);
    console.log('实际:', result2);
    console.log('匹配:', result2 === expected2);
    
    expect(result2).toBe(expected2);
  });

  test('应该保留矩阵环境中的行结束符', () => {
    // 测试用例3: 矩阵环境中的双反斜杠应该保留
    const input3 = '$$\\\\begin{pmatrix} a & b \\\\\\\\ c & d \\\\end{pmatrix}$$';
    const expected3 = '$$\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}$$';
    const result3 = unescapeLatexFormulas(input3);
    
    console.log('测试用例3:');
    console.log('输入:', input3);
    console.log('期望:', expected3);
    console.log('实际:', result3);
    console.log('匹配:', result3 === expected3);
    
    expect(result3).toBe(expected3);
  });

  test('应该处理混合情况', () => {
    // 测试用例4: 混合情况 - 既有命令转义又有下划线转义
    const input4 = '$$\\\\frac{\\\\partial f}{\\\\partial x\\_{1}} + \\\\sum\\_{i=1}^{n} x\\_{i}$$';
    const expected4 = '$$\\frac{\\partial f}{\\partial x_{1}} + \\sum_{i=1}^{n} x_{i}$$';
    const result4 = unescapeLatexFormulas(input4);
    
    console.log('测试用例4:');
    console.log('输入:', input4);
    console.log('期望:', expected4);
    console.log('实际:', result4);
    console.log('匹配:', result4 === expected4);
    
    expect(result4).toBe(expected4);
  });

  test('应该处理美元符号转义', () => {
    // 测试用例5: 美元符号转义（原有功能）
    const input5 = '\\$x^2\\$';
    const expected5 = '$x^2$';
    const result5 = unescapeLatexFormulas(input5);
    
    console.log('测试用例5:');
    console.log('输入:', input5);
    console.log('期望:', expected5);
    console.log('实际:', result5);
    console.log('匹配:', result5 === expected5);
    
    expect(result5).toBe(expected5);
  });
});

// 手动运行测试的函数
export function runLatexUtilsTests() {
  console.log('=== LaTeX Utils 测试开始 ===');
  
  // 测试用例1
  const input1 = '$$f\\\\left(x\\_{2}\\\\right)-f\\\\left(x\\_{1}\\\\right)=f^{\\\\prime}(\\\\xi)\\\\left(x\\_{2}-x\\_{1}\\\\right)$$';
  const result1 = unescapeLatexFormulas(input1);
  console.log('测试1 - 行间公式反斜杠转义:');
  console.log('输入:', input1);
  console.log('输出:', result1);
  console.log('');
  
  // 测试用例2
  const input2 = '$\\\\alpha + \\\\beta = \\\\gamma$';
  const result2 = unescapeLatexFormulas(input2);
  console.log('测试2 - 行内公式反斜杠转义:');
  console.log('输入:', input2);
  console.log('输出:', result2);
  console.log('');
  
  // 测试用例3
  const input3 = '$$\\\\begin{pmatrix} a & b \\\\\\\\ c & d \\\\end{pmatrix}$$';
  const result3 = unescapeLatexFormulas(input3);
  console.log('测试3 - 矩阵环境:');
  console.log('输入:', input3);
  console.log('输出:', result3);
  console.log('');
  
  console.log('=== LaTeX Utils 测试结束 ===');
  
  return {
    test1: { input: input1, output: result1 },
    test2: { input: input2, output: result2 },
    test3: { input: input3, output: result3 }
  };
}
