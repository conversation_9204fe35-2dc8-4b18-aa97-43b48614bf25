@import "./base.css";
@import "./reset.css";
#app {
  min-height: 100vh;
  min-width: 100vw;
  background-color: #ffffff;
  /* position: relative; */
  ::selection {
    background: #1973cb;
    color: #ffffff;
  }
}
::-webkit-scrollbar {
  position: absolute;
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background: var(--color-grey);
}
/* 项目名称预览模式样式 */
.preview-container {
  width: 1036px;
  min-height: 35px;
  box-sizing: border-box;
  position: relative;
  padding: 0 12px;
  border: 2px dashed #d0d0d0;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  
}

.preview-container:hover {
  border-color: var(--color-primary);
  background-color: #f8f9ff;
}

.preview-hint {
  position: absolute;
  bottom: 0px;
  right: 12px;
  font-size: 12px;
  color: #999;
  padding: 0px 5px;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preview-container:hover .preview-hint {
  opacity: 1;
}

.ellipsis-text {
  display: flex;
  max-width: calc(100% - 10px); /* 为引号预留更多空间 */
  // color: black; /* 确保文字为黑色 */
  position: relative; /* 为渐变遮罩定位 */
  align-items: center; /* 垂直居中 */
  overflow: hidden;
  height: 100%;
}
/* 省略号文本样式 */
.ellipsis-text-inline {
  max-width: calc(100% - 10px); /* 为引号预留更多空间 */
  position: relative; /* 为渐变遮罩定位 */
  align-items: center; /* 垂直居中 */
  overflow: hidden;
  height: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// .ellipsis-text::after {
//   content: "";
//   position: absolute;
//   top: 0;
//   right: 0;
//   width: 10px; /* 渐变区域宽度 */
//   height: 100%;
//   background: linear-gradient(
//     to right,
//     rgba(255, 255, 255, 0),
//     white
//   ); /* 从透明到白色的渐变 */
//   pointer-events: none; /* 确保不影响鼠标事件 */
// }
.ellipsis-text > p,
.ellipsis-text > span,
.ellipsis-text-inline p {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle; /* 垂直居中 */
}
.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-center {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative; /* 为绝对定位提供参考 */
}
.flex-start {
  display: flex;
  align-items: flex-start;
}
.flex-space-between {
  display: flex;
  justify-content: space-between;
}
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  // background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
  background: white;
  max-width: 500px;
  text-wrap: wrap;
  color: black;
  z-index: 9999 !important;
  box-shadow: 2px 2px 4px 5px #dcdfe6;
}

.el-popper.is-customized .el-popper__arrow::before {
  // background: linear-gradient(45deg, #b2e68d, #bce689);
  background: white;
  right: 0;
}
.titlefont {
  font-family: var(--title-family);
  font-weight: 600;
  font-style: normal;
}

.textfont {
  font-family: var(--text-family);
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  .titlefont();
  font-weight: 600; /* 假设 titlefont 具有较粗的字重 */
  margin: 0; /* 重置默认外边距 */
  padding: 0; /* 重置默认内边距 */
}

h1 {
  font-size: 24px;
}

h2 {
  font-size: 22px;
}

h3 {
  font-size: 20px;
}

h4 {
  font-size: 18px;
}

h5 {
  font-size: 16px;
}

h6 {
  font-size: 14px;
}
/* 使用深度选择器来作用于v-html渲染的表格内容 */
table:not(.el-table *, .vditor-reset *) {
  border: 1px solid #ddd !important;
  border-collapse: collapse;
  background-color: #fff;
  thead {
    background-color: #f5f5f5;
  }

  tr {
    border-bottom: 1px solid #eee;
  }

  th,
  td {
    padding: 8px 12px;
    text-align: left;
    border-right: 1px solid #eee;
    vertical-align: top;
  }

  th {
    font-weight: bold;
    background-color: #f0f0f0;
    color: #333;
  }
}

// 行内公式取消默认样式
code:not(.vditor-reset code) {
  padding: 0.065em 0.4em;
  word-break: break-word;
  overflow-x: auto;
  background-color: #fff4f4 !important;
  border-radius: 2px;
  color: #c2185b !important;
  span {
    color: #c2185b !important;
  }
}
/* 覆盖highlight.js库的.highlight样式 */
.highlight {
  color: inherit !important;
  background-color: transparent !important;
  cursor: inherit !important;
}

.highlightHover {
  text-decoration: none !important;
  font-size: 1.02em;
  background-color: #d6e9f6 !important;
  transform-origin: center;
  transition: background-color 0.2s ease-in-out;
  position: relative;
  z-index: 999;
}
.highlightHover .equation {
  background-color: #d6e9f6 !important;
  text-decoration: none;
  font-size: 1.02em;
  transform-origin: center;
  transition: background-color 0.2s ease-in-out;
  position: relative;
  z-index: 10;
}

/* 确保内部元素不继承下划线，但排除floating-content */
.highlightHover *:not(.floating-content):not(.floating-content *) {
  text-decoration: none !important;
}
/* 滚动条样式 - 默认隐藏，hover时显示 **/
.hover-scrollbar {
  /* 现代浏览器支持 - 预留滚动条空间 */
  scrollbar-gutter: stable;

  /* 兼容性方案 - 始终显示滚动条轨道 */
  &::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(220, 223, 230, 0); /* 默认半透明 */
    border-radius: 3px;
    transition: background-color 0.3s ease;
  }

  &:hover::-webkit-scrollbar-thumb {
    background-color: #dcdfe6; /* hover时完全不透明 */
  }
}

.primary {
  --el-color-primary: var(--color-primary);
}

.ck.ck-balloon-panel.ck-powered-by-balloon[class*="position_border"] {
  display: none;
}

.noteDrawer .el-drawer__body {
  padding: 0 20px;
  &::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 6px;
  }
}
.answerDrawer .el-drawer__body {
  padding: 0;
  &::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 6px;
  }
}

.question-content .keyWords,
// 针对DraftQuestion.vue中的keyWords
.question-info-row .keyWords ,
.description .keyWords
.keyword-container,.keyWords {
  // 通用keyWords选择器作为兜底 {
  max-width: 100% !important;
  line-height: 25px !important;
  & *:not(.equation *):not(.inline-equation *) {
    display: inline-block !important;
  }

  // 公式元素样式
  .equation {
    display: inline-block !important;
    cursor: pointer !important;
    transition: opacity 0.2s ease !important;
    // overflow: hidden;
    .textfont();
    font-size: 8px !important;
    base {
      max-height: 25px !important;
    }
  }

  // 图片元素样式
  img {
    display: inline !important;
    height: 14px !important;
    cursor: pointer !important;
    transition: opacity 0.2s ease !important;
  }
}
.select_to_ask_time {
  user-select: none;
}
.floatingContainer {
  z-index: 10001;
}

.floating-content {
  padding: 8px 12px;
  background: var(--color-primary);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family: "阿里巴巴普惠体 3.0 55 L3", "阿里巴巴普惠体 3.0 55",
    "阿里巴巴普惠体 3.0", sans-serif;
  font-size: 14px;
  font-weight: 400;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  max-width: 100%;
  overflow: hidden;
  min-width: 150px;
  animation: fadeIn 0.2s ease-in-out;

  .floating-content-item {
    padding: 0;
    transition: all 0.2s ease;
    max-width: 400px;
    &:hover {
      font-weight: 700;
      cursor: pointer;
      transform: translateX(2px);
    }
  }
}
