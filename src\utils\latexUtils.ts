/**
 * 处理字符串中的所有类型LaTeX公式（包括行内公式$...$和行间公式$$...$$），并用适当的元素包裹
 * @param {string} text - 包含LaTeX公式的输入文本
 * @returns {string} 处理后的文本，其中LaTeX公式被适当的元素包裹
 */
import { renderMarkdown } from "@/utils/markdown";
import URLParse from "url-parse";
import { decodeHTML } from "entities";

export function processAllLatexEquations(text: string) {
  if (!text) return "";

  try {
    // 首先，查找并处理行间公式 ($$...$$)
    const displayMatches = [];
    const displayRegex = /\$\$(.*?)\$\$/gs; // 's'标志用于多行匹配
    let displayMatch: RegExpExecArray | null;

    while ((displayMatch = displayRegex.exec(text)) !== null) {
      displayMatches.push({
        start: displayMatch.index,
        end: displayMatch.index + displayMatch[0].length,
        content: displayMatch[1].trim(),
      });
    }

    // 从后向前处理行间公式匹配项
    let result = text;
    for (let i = displayMatches.length - 1; i >= 0; i--) {
      let { start, end, content } = displayMatches[i];

      try {
        // 清理LaTeX内容，处理特殊字符
        content = cleanLatexContent(content);

        const mathString = `$$${content}$$`;
        // console.log("[latexUtils] 行间公式原始内容:", content);
        // console.log("[latexUtils] 行间公式传给renderMarkdown的内容:", mathString);
        const renderedContent = renderMarkdown(mathString);
        // console.log("[latexUtils] 行间公式渲染后的内容:", renderedContent);

        // 处理renderedContent中可能存在的段落标签
        let processedContent = renderedContent;
        // 移除开头和结尾的<p>标签
        processedContent = processedContent.replace(/^\s*<p>|<\/p>\s*$/g, "");

        const replacement = `<div class="equation" latexcode="${content}" >${processedContent}</div>`;
        result =
          result.substring(0, start) + replacement + result.substring(end);
      } catch (err) {
        console.error("[latexUtils] 处理行间公式时出错:", err);
        // 出错时保留原始公式
        const replacement = `<div class="equation">${content}</div>`;
        result =
          result.substring(0, start) + replacement + result.substring(end);
      }
    }

    // 然后查找并处理行内公式 ($...$)，排除那些是行间公式一部分的内容
    const inlineMatches = [];
    const inlineRegex = /\$(.*?)\$/g;
    let inlineMatch: RegExpExecArray | null;

    while ((inlineMatch = inlineRegex.exec(result)) !== null) {
      // 如果这是行间公式($$...$$)的一部分，则跳过
      const prevChar = result.charAt(inlineMatch.index - 1);
      const nextChar = result.charAt(inlineMatch.index + inlineMatch[0].length);
      if (prevChar === "$" || nextChar === "$") continue;

      inlineMatches.push({
        start: inlineMatch.index,
        end: inlineMatch.index + inlineMatch[0].length,
        content: inlineMatch[1].trim(),
      });
    }

    // 从后向前处理行内公式匹配项
    for (let i = inlineMatches.length - 1; i >= 0; i--) {
      let { start, end, content } = inlineMatches[i];

      try {
        // 清理LaTeX内容，处理特殊字符
        content = cleanLatexContent(content);

        const mathString = `$${content}$`;
        // console.log("[latexUtils] 行内公式传给renderMarkdown的内容:", mathString);
        const renderedContent = renderMarkdown(mathString);
        // console.log("[latexUtils] 行内公式渲染后的内容:", renderedContent);

        // 处理renderedContent中可能存在的换行符和段落标签
        let processedContent = renderedContent;
        // 移除开头和结尾的<p>标签
        processedContent = processedContent.replace(/^\s*<p>|<\/p>\s*$/g, "");
        // 移除所有的换行符
        processedContent = processedContent.replace(/\n/g, "");

        // 保存规范化后的LaTeX代码，以便在提问模式下使用和匹配

        const replacement = `<span class="inline-equation" latexcode="${content}">${processedContent}</span>`;
        result =
          result.substring(0, start) + replacement + result.substring(end);
      } catch (err) {
        console.error("[latexUtils] 处理行内公式时出错:", err);
        // 出错时保留原始公式
        const replacement = `<span class="inline-equation">${content}</span>`;
        result =
          result.substring(0, start) + replacement + result.substring(end);
      }
    }

    // 最后渲染整个文档
    try {
      return renderMarkdown(result);
    } catch (err) {
      console.error("[latexUtils] 渲染最终文档时出错:", err);
      return result; // 出错时返回部分处理的结果
    }
  } catch (err) {
    console.error("[latexUtils] 处理LaTeX公式时出错:", err);
    // 出错时返回原始文本
    return text;
  }
}

/**
 * 处理字符串中的LaTeX公式，只提取公式内容而不进行渲染
 * 这个函数适用于当renderMarkdown函数不能正确渲染LaTeX公式的情况
 * @param {string} text - 包含LaTeX公式的输入文本
 * @returns {string} 处理后的文本，其中LaTeX公式被适当的元素包裹
 */

/**
 * 将文本中的数学标签转换为LaTeX格式
 * 将<script type="math/tex">...</script>转换为$...$
 * 将<script type="math/tex; mode=display">...</script>转换为$$...$$
 * @param text - 包含数学标签的输入文本
 * @returns 转换后的文本，其中数学标签被转换为LaTeX格式
 */
export function convertMathTagsToMDLatex(text: string): string {
  if (!text) return "";

  let result = text;

  // 转换行间公式 <script type="math/tex; mode=display">...</script> 为 $$...$$
  const displayRegex =
    /<script type="math\/tex; mode=display">(.*?)<\/script>/gs;
  result = result.replace(displayRegex, (_match: string, content: string) => {
    return `$$${content}$$`;
  });

  // 转换行内公式 <script type="math/tex">...</script> 为 $...$
  const inlineRegex = /<script type="math\/tex">(.*?)<\/script>/gs;
  result = result.replace(inlineRegex, (_match: string, content: string) => {
    return `$${content}$`;
  });

  return result;
}

/**
 * 将HTML图片标签转换为Markdown格式的图片链接
 * 例如：<img src="https://example.com/path/filename.jpg" alt="filename.jpg">
 * 转换为：![filename.jpg](path/filename.jpg)
 * @param {string} text - 包含HTML图片标签的输入文本
 * @returns {string} 转换后的文本，其中HTML图片标签被转换为Markdown格式
 */
export function convertImgTagsToMarkdown(text: string): string {
  if (!text) return "";

  let result = text;

  // 匹配HTML图片标签，处理带引号和不带引号的情况
  const imgRegex =
    /<img\s+src=(?:\\?"|')?([^"'>]+)(?:\\?"|')?(?:\s+alt=(?:\\?"|')?([^"'>]*)(?:\\?"|')?)?[^>]*>/g;

  result = result.replace(
    imgRegex,
    (_match: string, src: string, alt: string = "") => {
      // 解码HTML实体
      src = src.replace(/&amp;/g, "&");

      try {
        // 提取日期和文件ID部分 - 匹配格式：2025-04-27/987bf988ded54be7aabb81e985f23f78_infinity2481958.jpg
        const dateFilePattern = /(\d{4}-\d{2}-\d{2}\/[a-f0-9]+_[^/?]+)/;
        const dateMatch = src.match(dateFilePattern);

        let path = "";

        if (dateMatch && dateMatch[1]) {
          // 如果找到日期和文件ID格式的部分，使用它
          path = dateMatch[1];
        } else {
          // 如果没有找到日期格式，尝试提取文件名
          const urlParts = src.split("/");
          if (urlParts.length > 0) {
            const lastPart = urlParts[urlParts.length - 1].split("?")[0];
            path = lastPart;
          }
        }

        // 如果还是没有提取到路径，使用整个src
        if (!path) {
          path = src.split("?")[0];
        }

        // 使用alt作为显示文本，如果没有alt则使用文件名
        const fileName = path.split("/").pop() || "";
        const displayText = alt || fileName || "image";

        // 返回Markdown格式的图片链接
        return `![${displayText}](${path})`;
      } catch (error) {
        console.error("转换图片标签时出错:", error);
        return _match; // 出错时返回原始标签
      }
    }
  );

  return result;
}
/**
 * 将HTML img标签中的长链接转换为短路径格式
 * 例如：<img src="https://example.com/path/filename.jpg?参数" alt="filename.jpg">
 * 转换为：<img src="path/filename.jpg" alt="filename.jpg">
 * @param {string} content - 包含HTML img标签的输入文本
 * @returns {string} 转换后的文本，其中img标签的src长链接被转换为短路径
 */
export function convertImgTagLongUrls(content: string): string {
  if (!content) return "";

  let result = content;

  // 匹配HTML img标签，捕获src属性值
  // 支持单引号、双引号和无引号的情况
  const imgTagRegex =
    /<img\s+([^>]*?)src\s*=\s*(['"]?)([^'">\s]+)\2([^>]*?)>/gi;

  result = result.replace(
    imgTagRegex,
    (
      match: string,
      beforeSrc: string,
      quote: string,
      srcUrl: string,
      afterSrc: string
    ) => {
      try {
        // 去掉URL两端的空格
        srcUrl = srcUrl.trim();

        // 检查是否是完整的HTTP/HTTPS URL
        if (!srcUrl.startsWith("http://") && !srcUrl.startsWith("https://")) {
          // 如果不是完整URL，保持原样
          return match;
        }

        // 使用URLParse解析URL
        const parsedUrl = new URLParse(srcUrl);

        // 获取pathname
        let pathname = parsedUrl.pathname;

        // 去掉开头的斜杠
        if (pathname.startsWith("/")) {
          pathname = pathname.substring(1);
        }

        // 如果pathname为空或只是斜杠，尝试从完整URL中提取文件名
        if (!pathname || pathname === "") {
          const urlParts = srcUrl.split("/");
          if (urlParts.length > 0) {
            const lastPart = urlParts[urlParts.length - 1];
            // 去掉查询参数和fragment
            pathname = lastPart.split("?")[0].split("#")[0];
          }
        }

        // 如果还是没有有效路径，保持原样
        if (!pathname || pathname === "") {
          return match;
        }

        // 重新构建img标签，保持其他属性不变
        return `<img ${beforeSrc}src${
          quote ? `=${quote}${pathname}${quote}` : `=${pathname}`
        }${afterSrc}>`;
      } catch (error) {
        console.error("转换img标签长链接时出错:", error);
        // 出错时返回原始内容
        return match;
      }
    }
  );

  return result;
}
/**
 * 规范化LaTeX公式内容，确保处理一致性
 * 处理反斜杠和HTML实体编码，使公式能够正确渲染
 * @param content LaTeX公式内容
 * @returns 规范化后的内容
 */
export function cleanLatexContent(content: string): string {
  if (!content) return "";

  let cleanContent = content;

  // 1. 处理HTML实体编码
  // 将&amp;转换回&符号，这是关键步骤，确保HTML实体在传递给KaTeX前被正确解码
  cleanContent = cleanContent.replace(/&amp;/g, "&");

  // 2. 处理末尾的任意数量的反斜杠+任意空白字符
  cleanContent = cleanContent.replace(/\\[\s\n]*$/g, ""); // 3. 处理矩阵中的行结束符
  // 确保矩阵中的\\被正确处理，避免被错误解析为单个反斜杠
  // 在pmatrix、bmatrix等环境中，\\用于表示行结束
  cleanContent = cleanContent.replace(
    /(\\begin\{[a-z]*matrix\}[\s\S]*?)(\\\\)([\s\S]*?\\end\{[a-z]*matrix\})/g,
    (_, before, lineBreak, after) => {
      // 保持\\作为行结束符
      return before + lineBreak + after;
    }
  );

  // 4. 处理换行符
  // 移除公式开头和结尾的换行符，保留公式内部的换行符
  cleanContent = cleanContent.replace(/^\n+|\n+$/g, "");

  // 5. 规范化LaTeX命令中的反斜杠
  // 将多个连续反斜杠(\\\)规范化为单个反斜杠(\)
  // 这是关键步骤，确保无论输入中有多少个反斜杠，都规范化为正确的数量

  // 先处理命令前的反斜杠：确保命令前只有一个反斜杠
  cleanContent = cleanContent.replace(
    /\\{2,}(?=begin|end|left|right|mathrm|frac|sum|int|lim|infty|partial|nabla|alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega)/g,
    "\\"
  );

  // 6. 处理下划线和大括号前的反斜杠
  // 在LaTeX中，\_表示文本下划线，而_表示下标
  // 在匹配时，我们希望统一使用_作为下标
  cleanContent = cleanContent.replace(/\\_/g, "_");
  cleanContent = cleanContent.replace(/\\{/g, "{");
  cleanContent = cleanContent.replace(/\\}/g, "}");

  // 7. 处理注释
  // 移除LaTeX注释行（以%开头的内容）
  cleanContent = cleanContent.replace(/\s*%.*$/gm, "");

  // 保留其他必要的反斜杠，如\prime, \cdot等

  return cleanContent;
}
//
/**
 * 将长链接的markdown图片语法转换为短路径格式
 * 例如：![filename.jpg](https://example.com/path/filename.jpg?参数)
 * 转换为：![filename.jpg](path/filename.jpg)
 * @param {string} content - 包含markdown图片链接的输入文本
 * @returns {string} 转换后的文本，其中长链接被转换为短路径
 */
export function convertLongUrlMarkdownImages(content: string): string {
  if (!content) return "";
  let result = content;
  // 匹配markdown图片语法：![alt text](url)
  const markdownImgRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;

  result = result.replace(
    markdownImgRegex,
    (match: string, altText: string, url: string) => {
      try {
        // 去掉URL两端的空格
        url = url.trim();

        // 检查是否是完整的HTTP/HTTPS URL
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
          // 如果不是完整URL，保持原样
          return match;
        }

        // 使用URLParse解析URL
        const parsedUrl = new URLParse(url);

        // 获取pathname
        let pathname = parsedUrl.pathname;

        // 去掉开头的斜杠
        if (pathname.startsWith("/")) {
          pathname = pathname.substring(1);
        }

        // 如果pathname为空或只是斜杠，尝试从完整URL中提取文件名
        if (!pathname || pathname === "") {
          const urlParts = url.split("/");
          if (urlParts.length > 0) {
            const lastPart = urlParts[urlParts.length - 1];
            // 去掉查询参数和fragment
            pathname = lastPart.split("?")[0].split("#")[0];
          }
        }

        // 如果还是没有有效路径，保持原样
        if (!pathname || pathname === "") {
          return match;
        }

        // 返回转换后的markdown格式
        return `![${altText}](${pathname})`;
      } catch (error) {
        console.error("转换markdown图片链接时出错:", error);
        // 出错时返回原始内容
        return match;
      }
    }
  );

  return result;
}

/**
 * 将language-math标签转换为script标签格式
 * 将<span class="language-math">公式</span>转换为<script type="math/tex">公式</script>
 * 将<div class="language-math">公式</div>转换为<script type="math/tex; mode=display">公式</script>
 * @param text - 包含language-math标签的输入文本
 * @returns 转换后的文本，其中language-math标签被转换为script标签格式
 */
export function convertLanguageMathToScript(text: string): string {
  if (!text) return "";

  let result = text;

  // 处理块级公式 <div class="language-math">公式</div>
  // 使用非贪婪匹配和多行模式
  const divRegex = /<div\s+class="language-math"[^>]*>(.*?)<\/div>/gs;
  result = result.replace(divRegex, (_, formula) => {
    // 去除公式内容两端的空白字符
    const cleanFormula = decodeHTML(formula.trim());

    return `<script type="math/tex; mode=display">${cleanFormula}</script>`;
  });

  // 处理行内公式 <span class="language-math">公式</span>
  // 使用非贪婪匹配
  const spanRegex = /<span\s+class="language-math"[^>]*>(.*?)<\/span>/g;
  result = result.replace(spanRegex, (_, formula) => {
    // 去除公式内容两端的空白字符
    const cleanFormula = formula.trim();
    return `<script type="math/tex">${cleanFormula}</script>`;
  });

  return result;
}
export function convertMathFormulas(text: string): string {
  if (!text) return "";

  // 查找所有需要排除的标签位置（code 标签）
  function getCodeRanges(
    content: string
  ): Array<{ start: number; end: number }> {
    const ranges: Array<{ start: number; end: number }> = [];
    const codeRegex = /<code[^>]*>.*?<\/code>/gs;
    let match;
    while ((match = codeRegex.exec(content)) !== null) {
      ranges.push({
        start: match.index,
        end: match.index + match[0].length,
      });
    }
    return ranges;
  }

  // 检查位置是否在 code 标签内
  function isInCodeTag(
    position: number,
    ranges: Array<{ start: number; end: number }>
  ): boolean {
    return ranges.some(
      (range) => position >= range.start && position < range.end
    );
  }

  let result = text;
  const codeRanges = getCodeRanges(result);

  // 第一步：处理行间公式 $$...$$（优先处理）
  const displayMatches: Array<{ start: number; end: number; formula: string }> =
    [];
  const displayRegex = /\$\$\s*(.*?)\s*\$\$/gs;
  let match;

  while ((match = displayRegex.exec(result)) !== null) {
    // 检查是否在 code 标签内
    if (!isInCodeTag(match.index, codeRanges)) {
      displayMatches.push({
        start: match.index,
        end: match.index + match[0].length,
        formula: match[1].trim(),
      });
    }
  }

  // 从后往前替换行间公式，避免索引变化
  for (let i = displayMatches.length - 1; i >= 0; i--) {
    const { start, end, formula } = displayMatches[i];
    const replacement = `<script type="math/tex; mode=display">${formula}</script>`;
    result = result.substring(0, start) + replacement + result.substring(end);
  }

  // 第二步：处理行内公式 $...$
  // 重新获取排除范围（包括 code 标签和已转换的 script 标签）
  function getExcludedRanges(
    content: string
  ): Array<{ start: number; end: number }> {
    const ranges: Array<{ start: number; end: number }> = [];

    // code 标签
    const codeRegex = /<code[^>]*>.*?<\/code>/gs;
    let match;
    while ((match = codeRegex.exec(content)) !== null) {
      ranges.push({ start: match.index, end: match.index + match[0].length });
    }

    // script 标签
    const scriptRegex = /<script\s+type="math\/tex[^"]*">.*?<\/script>/gs;
    while ((match = scriptRegex.exec(content)) !== null) {
      ranges.push({ start: match.index, end: match.index + match[0].length });
    }

    return ranges;
  }

  const excludedRanges = getExcludedRanges(result);

  // 找到所有单独的 $ 符号位置
  const dollarPositions: number[] = [];
  for (let i = 0; i < result.length; i++) {
    if (result[i] === "$" && !isInCodeTag(i, excludedRanges)) {
      // 确保不是 $$ 的一部分
      const prevChar = i > 0 ? result[i - 1] : "";
      const nextChar = i < result.length - 1 ? result[i + 1] : "";
      if (prevChar !== "$" && nextChar !== "$") {
        dollarPositions.push(i);
      }
    }
  }

  // 处理成对的 $ 符号，但要确保它们之间没有已转换的标签
  const inlineMatches: Array<{ start: number; end: number; formula: string }> =
    [];
  for (let i = 0; i < dollarPositions.length - 1; i += 2) {
    const startPos = dollarPositions[i];
    const endPos = dollarPositions[i + 1];

    // 检查这两个 $ 之间是否包含已转换的标签
    const betweenText = result.substring(startPos, endPos + 1);
    const hasScriptTag = /<script\s+type="math\/tex[^"]*">.*?<\/script>/s.test(
      betweenText
    );

    if (!hasScriptTag) {
      const formula = result.substring(startPos + 1, endPos).trim();
      if (formula && !formula.includes("\n")) {
        inlineMatches.push({
          start: startPos,
          end: endPos + 1,
          formula: formula,
        });
      }
    }
  }

  // 从后往前替换行内公式
  for (let i = inlineMatches.length - 1; i >= 0; i--) {
    const { start, end, formula } = inlineMatches[i];
    const replacement = `<script type="math/tex">${formula}</script>`;
    result = result.substring(0, start) + replacement + result.substring(end);
  }

  return result;
}
export function revertMathScriptsToMdReal(text: string): string {
  if (!text) return "";

  // 块级公式转为$$公式$$
  const displayRegex =
    /<script\s+type="math\/tex;\s*mode=display">(.*?)<\/script>/gs;
  let result = text.replace(displayRegex, (_match, formula) => {
    return `$$${formula}$$`;
  });

  // 行内公式转为$公式$
  const inlineRegex = /<script\s+type="math\/tex">(.*?)<\/script>/gs;
  result = result.replace(inlineRegex, (_match, formula) => {
    return `$${formula}$`;
  });
  return result;
}

/**
 * 保护数学公式的HTML转Markdown转换
 * 先提取数学公式，让vditor处理其他HTML内容，然后恢复数学公式
 * @param html HTML内容
 * @param vditorHtml2Md vditor的html2md方法
 * @returns 转换后的Markdown内容
 */
export function protectedHtml2Md(
  html: string,
  vditorHtml2Md: (html: string) => string
): string {
  if (!html) return "";

  // 存储提取的数学公式
  const mathFormulas: {
    placeholder: string;
    formula: string;
    type: "display" | "inline";
  }[] = [];
  let result = html;
  let placeholderIndex = 0;

  // 1. 提取并保护块级数学公式
  const displayRegex =
    /<script\s+type="math\/tex;\s*mode=display">(.*?)<\/script>/gs;
  result = result.replace(displayRegex, (_match, formula) => {
    const placeholder = `__MATH_DISPLAY_${placeholderIndex}__`;
    mathFormulas.push({
      placeholder,
      formula: formula.trim(),
      type: "display",
    });
    placeholderIndex++;
    console.log("[DEBUG] 提取块级公式:", {
      placeholder,
      formula: formula.trim(),
    });
    return placeholder;
  });

  // 2. 提取并保护行内数学公式
  const inlineRegex = /<script\s+type="math\/tex">(.*?)<\/script>/gs;
  result = result.replace(inlineRegex, (_match, formula) => {
    const placeholder = `__MATH_INLINE_${placeholderIndex}__`;
    mathFormulas.push({
      placeholder,
      formula: formula.trim(),
      type: "inline",
    });
    placeholderIndex++;
    console.log("[DEBUG] 提取行内公式:", {
      placeholder,
      formula: formula.trim(),
    });
    return placeholder;
  });

  // 3. 使用vditor处理剩余的HTML内容
  console.log("[DEBUG] 处理前的HTML (带占位符):", result);
  result = vditorHtml2Md(result);
  console.log("[DEBUG] vditor处理后的结果:", result);

  // 4. 恢复数学公式
  console.log("[DEBUG] 开始恢复数学公式，共", mathFormulas.length, "个");
  console.log("[DEBUG] 所有公式信息:", mathFormulas);
  console.log("[DEBUG] 恢复前的完整结果:", result);

  mathFormulas.forEach(({ placeholder, formula, type }) => {
    const mathMarkdown =
      type === "display" ? `$$${formula}$$\n` : `$${formula}$`;
    console.log("[DEBUG] 恢复公式:", {
      placeholder,
      formula,
      type,
      mathMarkdown,
    });

    // 尝试替换原始占位符
    if (result.includes(placeholder)) {
      console.log("[DEBUG] 找到原始占位符，直接替换");
      result = result.replace(placeholder, mathMarkdown);
    } else {
      // 如果原始占位符不存在，尝试替换被转义的占位符
      // __MATH_DISPLAY_0__ 可能被转义为 \_\_MATH\_DISPLAY\_0\_\_
      const escapedPlaceholder = placeholder.replace(/_/g, "\\_");
      console.log(
        "[DEBUG] 原始占位符未找到，尝试转义版本:",
        escapedPlaceholder
      );

      if (result.includes(escapedPlaceholder)) {
        console.log("[DEBUG] 找到转义占位符，替换");
        result = result.replace(escapedPlaceholder, mathMarkdown);
      } else {
        console.log("[DEBUG] 转义占位符也未找到，使用正则表达式");
        // 使用正则表达式进行更灵活的匹配
        const placeholderRegex = new RegExp(
          placeholder.replace(/_/g, "\\\\?_"),
          "g"
        );
        const beforeReplace = result;
        result = result.replace(placeholderRegex, mathMarkdown);
        console.log(
          "[DEBUG] 正则替换结果:",
          beforeReplace === result ? "未替换" : "已替换"
        );
      }
    }
    console.log("[DEBUG] 当前结果:", result);
  });

  console.log("[DEBUG] protectedHtml2Md 最终输出:", result);
  return result;
}
export function revertMathScriptsToMd(text: string): string {
  if (!text) return "";

  // 块级公式转为$$公式$$
  const displayRegex =
    /<script\s+type="math\/tex;\s*mode=display">(.*?)<\/script>/gs;
  let result = text.replace(displayRegex, (_match, formula) => {
    return `<p>$$${formula}$$</p>`;
  });

  // 行内公式转为$公式$
  const inlineRegex = /<script\s+type="math\/tex">(.*?)<\/script>/gs;
  result = result.replace(inlineRegex, (_match, formula) => {
    return `$${formula}$`;
  });
  return result;
}
// 处理LaTeX公式转义问题的辅助函数
export const unescapeLatexFormulas = (text: string): string => {
  // 处理各种LaTeX公式转义情况
  let result = text;

  // 情况1: 完全转义的公式 \$x^2\$ -> $x^2$
  result = result.replace(/\\\$([^$]*)\\\$/g, "$$$1$");
  // 情况2: 只有结尾被转义的公式 $x^2\$ -> $x^2$
  result = result.replace(/(\$[^$]*)\\\$/g, "$1$");
  // 情况3: 只有开头被转义的公式 \$x^2$ -> $x^2$
  result = result.replace(/\\\$([^$]*\$)/g, "$$$1");

  // 情况4: 处理LaTeX公式内部被错误转义的反斜杠
  // 在$$...$$和$...$公式内部，将双反斜杠\\恢复为单反斜杠\
  // 但要保留矩阵等环境中的行结束符\\

  // 先处理行间公式 $$...$$
  result = result.replace(
    /\$\$(.*?)\$\$/g,
    (_match: string, formula: string) => {
      // 处理公式内容中的反斜杠转义
      let processedFormula = formula;

      // 先保护矩阵环境中的行结束符\\（在begin{matrix}...end{matrix}之间）
      const matrixProtected: string[] = [];
      processedFormula = processedFormula.replace(
        /(\\begin\{[a-z]*matrix\})([\s\S]*?)(\\end\{[a-z]*matrix\})/g,
        (_matrixMatch: string, beginTag: string, matrixContent: string, endTag: string) => {
          const placeholder = `__MATRIX_${matrixProtected.length}__`;
          // 在矩阵内容中保留\\作为行结束符，但处理其他转义
          const processedMatrixContent = matrixContent.replace(/\\\\(?![\\])/g, '\\\\'); // 保持\\不变
          matrixProtected.push(beginTag + processedMatrixContent + endTag);
          return placeholder;
        }
      );

      // 处理LaTeX命令中的双反斜杠转义
      // 将\\left, \\right, \\prime等LaTeX命令中的双反斜杠恢复为单反斜杠
      processedFormula = processedFormula.replace(
        /\\\\(left|right|prime|frac|sum|int|lim|infty|partial|nabla|alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|mathrm|text|sqrt|cdot|times|div|pm|mp|leq|geq|neq|approx|equiv|propto|in|notin|subset|supset|subseteq|supseteq|cap|cup|emptyset|forall|exists|nabla|partial)/g,
        '\\$1'
      );

      // 处理下标和上标中的转义下划线 \_ -> _
      processedFormula = processedFormula.replace(/\\_/g, '_');

      // 恢复被保护的矩阵内容
      matrixProtected.forEach((matrixContent, index) => {
        processedFormula = processedFormula.replace(`__MATRIX_${index}__`, matrixContent);
      });

      return `$$${processedFormula}$$`;
    }
  );

  // 再处理行内公式 $...$（排除已经处理的$$...$$）
  result = result.replace(
    /(?<!\$)\$(?!\$)(.*?)(?<!\$)\$(?!\$)/g,
    (_match: string, formula: string) => {
      // 处理公式内容中的反斜杠转义
      let processedFormula = formula;

      // 先保护矩阵环境中的行结束符\\（在begin{matrix}...end{matrix}之间）
      const matrixProtected: string[] = [];
      processedFormula = processedFormula.replace(
        /(\\begin\{[a-z]*matrix\})([\s\S]*?)(\\end\{[a-z]*matrix\})/g,
        (
          _matrixMatch: string,
          beginTag: string,
          matrixContent: string,
          endTag: string
        ) => {
          const placeholder = `__MATRIX_${matrixProtected.length}__`;
          // 在矩阵内容中保留\\作为行结束符，但处理其他转义
          const processedMatrixContent = matrixContent.replace(
            /\\\\(?![\\])/g,
            "\\\\"
          ); // 保持\\不变
          matrixProtected.push(beginTag + processedMatrixContent + endTag);
          return placeholder;
        }
      );

      // 处理LaTeX命令中的双反斜杠转义
      // 将\\left, \\right, \\prime等LaTeX命令中的双反斜杠恢复为单反斜杠
      processedFormula = processedFormula.replace(
        /\\\\(left|right|prime|frac|sum|int|lim|infty|partial|nabla|alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|mathrm|text|sqrt|cdot|times|div|pm|mp|leq|geq|neq|approx|equiv|propto|in|notin|subset|supset|subseteq|supseteq|cap|cup|emptyset|forall|exists|nabla|partial)/g,
        "\\$1"
      );

      // 处理下标和上标中的转义下划线 \_ -> _
      processedFormula = processedFormula.replace(/\\_/g, "_");

      // 恢复被保护的矩阵内容
      matrixProtected.forEach((matrixContent, index) => {
        processedFormula = processedFormula.replace(
          `__MATRIX_${index}__`,
          matrixContent
        );
      });

      return `$${processedFormula}$`;
    }
  );

  return result;
};
